import pandas as pd

pd.set_option('display.max_columns', None)

from DatabaseManagement.ImportExport import get_table_from_GZ


all_cases_df = get_table_from_GZ("tb_case", force_refresh=True)

all_cases_df.columns

all_cases_df[all_cases_df['id'].isin([14416, 14425, 14422, 14420, 14419, 14415, 14421, 14423, 14418, 14417, 14424, 14426])]

len(all_cases_df)

ids_to_remove = [14416, 14425, 14422, 14420, 14419, 14415, 14421, 14423, 14418, 14417, 14424, 14426]
all_cases_df = all_cases_df[~all_cases_df['id'].isin(ids_to_remove)]

len(all_cases_df)

all_cases_df.head(1)

all_cases_df[all_cases_df['docket'] == '1:25-cv-11899']

fault_case = all_cases_df[all_cases_df['docket'] == '1:25-cv-11899']
fault_case

(all_cases_df.at[8075, 'images_status'] or {})

import json


print(json.loads((all_cases_df.at[8075, 'images_status'] or {})).get('ip_manager_state', {}))


json.loads((all_cases_df.at[8073, 'images_status'] or {}))

from datetime import date, timedelta
import calendar

def get_date_range_for_period(period_type):
    """
    Calculate date ranges for different periodic fetch types.
    
    Args:
        period_type: 'weekly', 'monthly_1', 'monthly_2', 'monthly_3'
    
    Returns:
        tuple: (start_date, end_date, description)
    """
    today = date.today()
    
    if period_type == 'weekly':
        # Last week (7 days ago to today)
        start_date = today - timedelta(days=7)
        end_date = today
        description = "last week"
        
    elif period_type == 'monthly_1':
        # Last month (previous month)
        if today.month == 1:
            # If current month is January, last month is December of previous year
            last_month = 12
            year = today.year - 1
        else:
            last_month = today.month - 1
            year = today.year
        
        # First day of last month
        start_date = date(year, last_month, 1)
        # Last day of last month
        last_day = calendar.monthrange(year, last_month)[1]
        end_date = date(year, last_month, last_day)
        description = f"{calendar.month_name[last_month]} {year}"
        
    elif period_type == 'monthly_2':
        # 2 months ago
        if today.month <= 2:
            target_month = today.month + 10  # 12 - (2 - month)
            year = today.year - 1
        else:
            target_month = today.month - 2
            year = today.year
        
        start_date = date(year, target_month, 1)
        last_day = calendar.monthrange(year, target_month)[1]
        end_date = date(year, target_month, last_day)
        description = f"{calendar.month_name[target_month]} {year}"
        
    elif period_type == 'monthly_3':
        # 3 months ago
        if today.month <= 3:
            target_month = today.month + 9  # 12 - (3 - month)
            year = today.year - 1
        else:
            target_month = today.month - 3
            year = today.year
        
        start_date = date(year, target_month, 1)
        last_day = calendar.monthrange(year, target_month)[1]
        end_date = date(year, target_month, last_day)
        description = f"{calendar.month_name[target_month]} {year}"
        
    else:
        raise ValueError(f"Unknown period_type: {period_type}")
    
    return start_date, end_date, description

today = date.today()
print(f"Today: {date.today()}")

last_month = today.month - 1
year = today.year
start_date = date(year, last_month, 1)

calendar.month_name[last_month]

periods = ['weekly', 'monthly_1', 'monthly_2', 'monthly_3']

for period in periods:
    try:
        start_date, end_date, description = get_date_range_for_period(period)
        print(f"{period:12}: {description:25} ({start_date} to {end_date})")
    except Exception as e:
        print(f"{period:12}: ERROR - {e}")

from datetime import date, timedelta
from dateutil.relativedelta import relativedelta

def get_month_range(months_ago):
    today = date.today()
    target_date = today - relativedelta(months=months_ago)
    start_date = target_date.replace(day=1)
    end_date = (start_date + relativedelta(months=1)) - timedelta(days=1)
    description = f"{start_date.strftime('%B %Y')}"
    return start_date, end_date, description

def get_date_range_for_period(period_type):
    today = date.today()

    if period_type == 'weekly':
        start_date = today - timedelta(days=7)
        end_date = today
        description = "last week"

    elif period_type == 'monthly_1':
        start_date, end_date, description = get_month_range(1)

    elif period_type == 'monthly_2':
        start_date, end_date, description = get_month_range(2)

    elif period_type == 'monthly_3':
        start_date, end_date, description = get_month_range(3)

    elif period_type == 'monthly_4':
        start_date = today - relativedelta(months=18)
        end_date = today - relativedelta(months=3)
        description = "open cases 3-18 months old"

    else:
        raise ValueError(f"Unknown period_type: {period_type}")

    return start_date, end_date, description


def get_month_range(months_ago):
    today = date.today()
    target_date = today - relativedelta(months=months_ago)
    start_date = target_date.replace(day=1)
    end_date = (start_date + relativedelta(months=1)) - timedelta(days=1)
    description = f"{start_date.strftime('%B %Y')}"
    return start_date, end_date, description

get_month_range(3)

